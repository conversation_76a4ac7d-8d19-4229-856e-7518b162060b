-- 调试版本的存储过程
ALTER PROCEDURE [dbo].[SP_K_DAY_Fmu_Debug]
    @sDateBegin varchar(10),
    @sDateEnd varchar(10),
    @FmuBMs varchar(100),
    @Debug BIT = 1  -- 调试模式开关
AS
BEGIN
    SET NOCOUNT ON;

    IF @Debug = 1
        PRINT '开始执行存储过程 SP_K_DAY_Fmu_Debug'

    BEGIN TRY

        -- 参数验证
        IF @sDateBegin IS NULL OR @sDateEnd IS NULL OR @FmuBMs IS NULL
        BEGIN
            RAISERROR('参数不能为空', 16, 1)
            RETURN
        END

        IF @Debug = 1
            PRINT '参数: 开始日期=' + @sDateBegin + ', 结束日期=' + @sDateEnd + ', 公式编码=' + @FmuBMs

        -- 创建公式设置临时表
        CREATE TABLE #FumSet(
            FMUBM INT NULL,
            Table_Name varchar(255) NULL
        )

        -- 动态插入公式设置数据
        DECLARE @SqlInsert NVARCHAR(500)
        SET @SqlInsert = 'INSERT INTO #FumSet (FMUBM,Table_Name) SELECT DISTINCT BM0000,PATBM FROM GZ_FmuSet WHERE BM0000 IN (' + @FmuBMs + ')'

        IF @Debug = 1
            PRINT '执行SQL: ' + @SqlInsert

        EXEC sp_executesql @SqlInsert

        DECLARE @FumCount INT
        SELECT @FumCount = COUNT(FMUBM) FROM #FumSet

        IF @Debug = 1
            PRINT '找到公式数量: ' + CAST(@FumCount AS VARCHAR(10))

        -- 检查是否有公式需要处理
        IF @FumCount = 0
        BEGIN
            PRINT '没有找到匹配的公式设置'
            RETURN
        END

        -- 检查基础表是否存在
        IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'K_Day')
        BEGIN
            RAISERROR('基础表 K_Day 不存在', 16, 1)
            RETURN
        END

        IF NOT EXISTS (SELECT 1 FROM sys.tables WHERE name = 'GZ_FMU')
        BEGIN
            RAISERROR('公式表 GZ_FMU 不存在', 16, 1)
            RETURN
        END

        -- 创建#K_Day临时表（如果不存在）
        IF OBJECT_ID('tempdb..#K_Day') IS NULL
        BEGIN
            IF @Debug = 1
                PRINT '创建 #K_Day 临时表'

            -- 检查是否存在A01表
            IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'A01')
            BEGIN
                SELECT K.*
                INTO #K_Day
                FROM K_Day K
                INNER JOIN A01 ON K.A0188 = A01.A0188
                WHERE K.duty_date >= @sDateBegin
                  AND K.duty_date <= @sDateEnd
            END
            ELSE
            BEGIN
                -- 如果A01表不存在，直接从K_Day表获取数据
                SELECT *
                INTO #K_Day
                FROM K_Day
                WHERE duty_date >= @sDateBegin
                  AND duty_date <= @sDateEnd
            END

            DECLARE @KDayCount INT
            SELECT @KDayCount = COUNT(*) FROM #K_Day

            IF @Debug = 1
                PRINT '#K_Day 记录数: ' + CAST(@KDayCount AS VARCHAR(10))
        END

        -- 删除已存在的#GZ_FMU临时表
        IF OBJECT_ID('tempdb..#GZ_FMU') IS NOT NULL
            DROP TABLE #GZ_FMU

        -- 创建公式临时表
        SELECT
            ROW_NUMBER() OVER(ORDER BY A.FMUBM, A.FormulaOrder) AS RowNum,
            CAST(A.Sql_Final AS NVARCHAR(MAX)) AS Sql_Final,
            A.Condition,
            B.Table_name,
            A.FMUBM,
            A.FormulaOrder
        INTO #GZ_FMU
        FROM GZ_FMU A
        INNER JOIN #FumSet B ON A.FMUBM = B.FmuBm
        WHERE A.Takeit = 1
        ORDER BY A.FMUBM, A.FormulaOrder

        DECLARE @FormulaCount INT
        SELECT @FormulaCount = COUNT(*) FROM #GZ_FMU

        IF @Debug = 1
            PRINT '找到有效公式数量: ' + CAST(@FormulaCount AS VARCHAR(10))

        IF @FormulaCount = 0
        BEGIN
            PRINT '没有找到有效的公式'
            RETURN
        END

        -- 显示前几个公式（调试用）
        IF @Debug = 1
        BEGIN
            PRINT '前5个公式:'
            SELECT TOP 5 RowNum, FMUBM, FormulaOrder, LEFT(Sql_Final, 100) AS Sql_Preview
            FROM #GZ_FMU
            ORDER BY RowNum
        END

        CREATE INDEX IX_GZ_FMU_RowNum ON #GZ_FMU (RowNum)

        -- 对公式中的特定字符进行替换
        IF @Debug = 1
            PRINT '开始替换公式中的表名'

        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Day.', '#K_Day.')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Day ', '#K_Day ')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Card.', '#K_Card.')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Card ', '#K_Card ')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Leave.', '#K_Leave.')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Leave ', '#K_Leave ')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Over.', '#K_Over.')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Over ', '#K_Over ')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Out.', '#K_Out.')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Out ', '#K_Out ')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Travel.', '#K_Travel.')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Travel ', '#K_Travel ')

        -- 添加开始结束日期的转换
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, '开始日期', '''' + @sDateBegin + '''')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, '结束日期', '''' + @sDateEnd + '''')

        -- 显示替换后的公式（调试用）
        IF @Debug = 1
        BEGIN
            PRINT '替换后的前3个公式:'
            SELECT TOP 3 RowNum, FMUBM, LEFT(Sql_Final, 200) AS Sql_After_Replace
            FROM #GZ_FMU
            ORDER BY RowNum
        END

        -- 声明变量
        DECLARE @i INT = 1
        DECLARE @ism INT
        DECLARE @Sql_Final NVARCHAR(MAX)
        DECLARE @CurrentSql NVARCHAR(MAX)

        SELECT @ism = MAX(RowNum) FROM #GZ_FMU

        IF @Debug = 1
            PRINT '开始逐个执行公式，总数: ' + CAST(@ism AS VARCHAR(10))

        -- 逐个执行公式（调试模式）
        WHILE @i <= @ism
        BEGIN
            SELECT @Sql_Final = Sql_Final FROM #GZ_FMU WHERE RowNum = @i

            IF @Sql_Final IS NOT NULL AND LEN(@Sql_Final) > 0
            BEGIN
                SET @CurrentSql = 'UPDATE #K_Day ' + @Sql_Final

                IF @Debug = 1
                    PRINT '执行公式 ' + CAST(@i AS VARCHAR(10)) + ': ' + LEFT(@CurrentSql, 200)

                BEGIN TRY
                    EXEC sp_executesql @CurrentSql

                    IF @Debug = 1
                        PRINT '公式 ' + CAST(@i AS VARCHAR(10)) + ' 执行成功'

                END TRY
                BEGIN CATCH
                    PRINT '公式 ' + CAST(@i AS VARCHAR(10)) + ' 执行失败: ' + ERROR_MESSAGE()
                    PRINT '失败的SQL: ' + @CurrentSql
                    -- 继续执行下一个公式，不中断整个过程
                END CATCH
            END
            ELSE
            BEGIN
                IF @Debug = 1
                    PRINT '公式 ' + CAST(@i AS VARCHAR(10)) + ' 为空，跳过'
            END

            SET @i = @i + 1
        END

        PRINT '公式计算完成'

    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY()
        DECLARE @ErrorState INT = ERROR_STATE()
        DECLARE @ErrorLine INT = ERROR_LINE()

        PRINT '存储过程执行出错:'
        PRINT '错误消息: ' + @ErrorMessage
        PRINT '错误行号: ' + CAST(@ErrorLine AS VARCHAR(10))

        -- 清理临时表
        IF OBJECT_ID('tempdb..#FumSet') IS NOT NULL DROP TABLE #FumSet
        IF OBJECT_ID('tempdb..#GZ_FMU') IS NOT NULL DROP TABLE #GZ_FMU

        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState)
    END CATCH

    -- 清理临时表
    IF OBJECT_ID('tempdb..#FumSet') IS NOT NULL DROP TABLE #FumSet
    IF OBJECT_ID('tempdb..#GZ_FMU') IS NOT NULL DROP TABLE #GZ_FMU

    IF @Debug = 1
        PRINT '存储过程执行完成'

END
