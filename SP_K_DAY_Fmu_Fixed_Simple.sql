ALTER PROCEDURE [dbo].[SP_K_DAY_Fmu] 
    @sDateBegin varchar(10),
    @sDateEnd varchar(10),
    @FmuBMs varchar(100)
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        
        /*日结果记录表 - 修复：取消注释并完善逻辑*/
        IF OBJECT_ID('tempdb..#K_Day') IS NULL
        BEGIN
            -- 检查A01表是否存在
            IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'A01')
            BEGIN
                SELECT K.* INTO #K_Day  
                FROM K_Day K, A01 
                WHERE K.A0188 = A01.A0188 
                  AND duty_date >= @sDateBegin 
                  AND duty_date <= @sDateEnd 
            END
            ELSE
            BEGIN
                -- 如果A01表不存在，直接从K_Day获取
                SELECT * INTO #K_Day  
                FROM K_Day 
                WHERE duty_date >= @sDateBegin 
                  AND duty_date <= @sDateEnd 
            END
        END

        CREATE TABLE #FumSet(FMUBM INT NULL, Table_Name varchar(255) null)
        EXEC('INSERT INTO #FumSet (FMUBM,Table_Name) SELECT DISTINCT BM0000,PATBM FROM GZ_FmuSet WHERE BM0000 IN ('+@FmuBMs+')')

        -- 修复：使用NVARCHAR(MAX)替代TEXT
        DECLARE @Sql_Final varchar(4000), @Sql_Final1 NVARCHAR(MAX), @Condition varchar(4000)
        DECLARE @where_index int
        DECLARE @Tabname varchar(255), @i int, @ism int

        IF (SELECT COUNT(FMUBM) from #FumSet) > 0
        BEGIN
            -- 删除已存在的临时表
            IF OBJECT_ID('tempdb..#GZ_FMU') IS NOT NULL
                DROP TABLE #GZ_FMU
                
            SELECT ROW_NUMBER() OVER(ORDER BY A.FMUBM, A.FormulaOrder) AS RowNum,
                   CAST(Sql_Final AS NVARCHAR(MAX)) Sql_Final,
                   Condition,
                   B.Table_name,
                   A.FMUBM,
                   A.FormulaOrder 
            INTO #GZ_FMU 
            FROM GZ_FMU A, #FumSet B
            WHERE A.FMUBM = B.FmuBm AND Takeit = 1
            ORDER BY A.FMUBM, A.FormulaOrder
            
            CREATE INDEX #GZ_FMU_Index ON #GZ_FMU (RowNum)

            --对公式中的特定字符进行替换
            UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final,'K_Day.','#K_Day.')
            UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final,'K_Day ','#K_Day ')
            UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final,'K_Card.','#K_Card.');
            UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final,'K_Card ','#K_Card ');			
            UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final,'K_Leave.','#K_Leave.');
            UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final,'K_Leave ','#K_Leave ');			
            UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final,'K_Over.','#K_Over.');
            UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final,'K_Over ','#K_Over ');	
            UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final,'K_Out.','#K_Out.');
            UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final,'K_Out ','#K_Out ');			
            UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final,'K_Travel.','#K_Travel.');
            UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final,'K_Travel ','#K_Travel ');	
            --添加开始结束日期的转换
            UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final,'开始日期',''''+@sDateBegin+'''')	
            UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final,'结束日期',''''+@sDateEnd+'''')

            SET @i = 1  
            SET @Sql_Final1 = N''
            SELECT @ism = MAX(RowNum) FROM #GZ_FMU
            
            -- 保持你的原始批处理逻辑
            WHILE @I <= @ism
            BEGIN
                SELECT @Sql_Final = Sql_Final FROM #GZ_FMU WHERE RowNum = @I
                
                IF @Sql_Final IS NOT NULL AND LEN(@Sql_Final) > 0
                BEGIN
                    SET @Sql_Final1 = @Sql_Final1 + N' UPDATE #K_Day ' + @Sql_Final + N';'   
                END
                
                SET @I = @I + 1
            END

            -- 执行批处理SQL
            IF LEN(@Sql_Final1) > 0
            BEGIN
                EXEC sp_executesql @Sql_Final1   
            END
        END
        
        PRINT '公式计算完成'
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
        PRINT '存储过程执行出错: ' + @ErrorMessage
        
        -- 清理临时表
        IF OBJECT_ID('tempdb..#FumSet') IS NOT NULL DROP TABLE #FumSet
        IF OBJECT_ID('tempdb..#GZ_FMU') IS NOT NULL DROP TABLE #GZ_FMU
        
        RAISERROR(@ErrorMessage, 16, 1)
    END CATCH
    
    -- 清理临时表
    IF OBJECT_ID('tempdb..#FumSet') IS NOT NULL DROP TABLE #FumSet
    IF OBJECT_ID('tempdb..#GZ_FMU') IS NOT NULL DROP TABLE #GZ_FMU
    
END
