ALTER PROCEDURE [dbo].[SP_K_DAY_Fmu] 
    @sDateBegin varchar(10),
    @sDateEnd varchar(10),
    @FmuBMs varchar(100)
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 错误处理
    BEGIN TRY
        
        -- 创建公式设置临时表
        CREATE TABLE #FumSet(
            FMUBM INT NULL,
            Table_Name varchar(255) NULL
        )
        
        -- 动态插入公式设置数据
        DECLARE @SqlInsert NVARCHAR(500)
        SET @SqlInsert = 'INSERT INTO #FumSet (FMUBM,Table_Name) SELECT DISTINCT BM0000,PATBM FROM GZ_FmuSet WHERE BM0000 IN (' + @FmuBMs + ')'
        EXEC sp_executesql @SqlInsert
        
        -- 检查是否有公式需要处理
        IF (SELECT COUNT(FMUBM) FROM #FumSet) = 0
        BEGIN
            PRINT '没有找到匹配的公式设置'
            RETURN
        END
        
        -- 创建#K_Day临时表（如果不存在）
        IF OBJECT_ID('tempdb..#K_Day') IS NULL
        BEGIN
            -- 这里需要根据实际的K_Day表结构来创建
            -- 示例结构，请根据实际情况调整
            SELECT K.* 
            INTO #K_Day  
            FROM K_Day K
            INNER JOIN A01 ON K.A0188 = A01.A0188 
            WHERE K.duty_date >= @sDateBegin 
              AND K.duty_date <= @sDateEnd
        END
        
        -- 创建其他必要的临时表
        IF OBJECT_ID('tempdb..#K_Card') IS NULL
        BEGIN
            SELECT * INTO #K_Card FROM K_Card WHERE 1=0 -- 创建空表结构
        END
        
        IF OBJECT_ID('tempdb..#K_Leave') IS NULL
        BEGIN
            SELECT * INTO #K_Leave FROM K_Leave WHERE 1=0
        END
        
        IF OBJECT_ID('tempdb..#K_Over') IS NULL
        BEGIN
            SELECT * INTO #K_Over FROM K_Over WHERE 1=0
        END
        
        IF OBJECT_ID('tempdb..#K_Out') IS NULL
        BEGIN
            SELECT * INTO #K_Out FROM K_Out WHERE 1=0
        END
        
        IF OBJECT_ID('tempdb..#K_Travel') IS NULL
        BEGIN
            SELECT * INTO #K_Travel FROM K_Travel WHERE 1=0
        END
        
        -- 删除已存在的#GZ_FMU临时表
        IF OBJECT_ID('tempdb..#GZ_FMU') IS NOT NULL
            DROP TABLE #GZ_FMU
            
        -- 创建公式临时表
        SELECT 
            ROW_NUMBER() OVER(ORDER BY A.FMUBM, A.FormulaOrder) AS RowNum,
            CAST(Sql_Final AS NVARCHAR(MAX)) AS Sql_Final,
            Condition,
            B.Table_name,
            A.FMUBM,
            A.FormulaOrder 
        INTO #GZ_FMU 
        FROM GZ_FMU A
        INNER JOIN #FumSet B ON A.FMUBM = B.FmuBm 
        WHERE A.Takeit = 1
        ORDER BY A.FMUBM, A.FormulaOrder
        
        CREATE INDEX IX_GZ_FMU_RowNum ON #GZ_FMU (RowNum)
        
        -- 对公式中的特定字符进行替换
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Day.', '#K_Day.')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Day ', '#K_Day ')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Card.', '#K_Card.')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Card ', '#K_Card ')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Leave.', '#K_Leave.')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Leave ', '#K_Leave ')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Over.', '#K_Over.')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Over ', '#K_Over ')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Out.', '#K_Out.')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Out ', '#K_Out ')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Travel.', '#K_Travel.')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Travel ', '#K_Travel ')
        
        -- 添加开始结束日期的转换
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, '开始日期', '''' + @sDateBegin + '''')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, '结束日期', '''' + @sDateEnd + '''')
        
        -- 声明变量
        DECLARE @i INT = 1
        DECLARE @ism INT
        DECLARE @Sql_Final NVARCHAR(MAX)
        DECLARE @Sql_Final1 NVARCHAR(MAX) = ''
        DECLARE @BatchSize INT = 10 -- 批处理大小，避免SQL过长
        
        SELECT @ism = MAX(RowNum) FROM #GZ_FMU
        
        -- 分批执行SQL，避免单个SQL过长
        WHILE @i <= @ism
        BEGIN
            SET @Sql_Final1 = ''
            DECLARE @BatchEnd INT = @i + @BatchSize - 1
            IF @BatchEnd > @ism SET @BatchEnd = @ism
            
            -- 构建批处理SQL
            WHILE @i <= @BatchEnd
            BEGIN
                SELECT @Sql_Final = Sql_Final FROM #GZ_FMU WHERE RowNum = @i
                
                IF @Sql_Final IS NOT NULL AND LEN(@Sql_Final) > 0
                BEGIN
                    SET @Sql_Final1 = @Sql_Final1 + ' UPDATE #K_Day ' + @Sql_Final + ';'
                END
                
                SET @i = @i + 1
            END
            
            -- 执行批处理
            IF LEN(@Sql_Final1) > 0
            BEGIN
                PRINT '执行SQL批次: ' + CAST(@i - @BatchSize AS VARCHAR(10)) + ' - ' + CAST(@i - 1 AS VARCHAR(10))
                EXEC sp_executesql @Sql_Final1
            END
        END
        
        PRINT '公式计算完成'
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY()
        DECLARE @ErrorState INT = ERROR_STATE()
        
        PRINT '存储过程执行出错: ' + @ErrorMessage
        
        -- 清理临时表
        IF OBJECT_ID('tempdb..#FumSet') IS NOT NULL DROP TABLE #FumSet
        IF OBJECT_ID('tempdb..#GZ_FMU') IS NOT NULL DROP TABLE #GZ_FMU
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState)
    END CATCH
    
    -- 清理临时表
    IF OBJECT_ID('tempdb..#FumSet') IS NOT NULL DROP TABLE #FumSet
    IF OBJECT_ID('tempdb..#GZ_FMU') IS NOT NULL DROP TABLE #GZ_FMU
    
END
