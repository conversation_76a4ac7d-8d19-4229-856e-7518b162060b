ALTER PROCEDURE [dbo].[SP_K_DAY_Fmu_Optimized] 
    @sDateBegin varchar(10),
    @sDateEnd varchar(10),
    @FmuBMs varchar(100)
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        
        -- 参数验证
        IF @sDateBegin IS NULL OR @sDateEnd IS NULL OR @FmuBMs IS NULL
        BEGIN
            RAISERROR('参数不能为空', 16, 1)
            RETURN
        END
        
        -- 创建公式设置临时表
        CREATE TABLE #FumSet(
            FMUBM INT NULL,
            Table_Name varchar(255) NULL
        )
        
        -- 动态插入公式设置数据（使用参数化查询更安全）
        DECLARE @SqlInsert NVARCHAR(500)
        SET @SqlInsert = N'INSERT INTO #FumSet (FMUBM,Table_Name) 
                          SELECT DISTINCT BM0000,PATBM 
                          FROM GZ_FmuSet 
                          WHERE BM0000 IN (' + @FmuBMs + ')'
        EXEC sp_executesql @SqlInsert
        
        -- 检查是否有公式需要处理
        IF (SELECT COUNT(FMUBM) FROM #FumSet) = 0
        BEGIN
            PRINT '没有找到匹配的公式设置'
            RETURN
        END
        
        -- 创建#K_Day临时表（修复原版本缺失的问题）
        IF OBJECT_ID('tempdb..#K_Day') IS NULL
        BEGIN
            -- 检查A01表是否存在，决定JOIN方式
            IF EXISTS (SELECT 1 FROM sys.tables WHERE name = 'A01')
            BEGIN
                SELECT K.* 
                INTO #K_Day  
                FROM K_Day K
                INNER JOIN A01 ON K.A0188 = A01.A0188 
                WHERE K.duty_date >= @sDateBegin 
                  AND K.duty_date <= @sDateEnd
            END
            ELSE
            BEGIN
                SELECT * 
                INTO #K_Day  
                FROM K_Day
                WHERE duty_date >= @sDateBegin 
                  AND duty_date <= @sDateEnd
            END
        END
        
        -- 创建其他可能需要的临时表（避免运行时错误）
        IF OBJECT_ID('tempdb..#K_Card') IS NULL
        BEGIN
            SELECT * INTO #K_Card FROM K_Card 
            WHERE duty_date >= @sDateBegin AND duty_date <= @sDateEnd
        END
        
        IF OBJECT_ID('tempdb..#K_Leave') IS NULL
        BEGIN
            SELECT * INTO #K_Leave FROM K_Leave 
            WHERE duty_date >= @sDateBegin AND duty_date <= @sDateEnd
        END
        
        IF OBJECT_ID('tempdb..#K_Over') IS NULL
        BEGIN
            SELECT * INTO #K_Over FROM K_Over 
            WHERE duty_date >= @sDateBegin AND duty_date <= @sDateEnd
        END
        
        IF OBJECT_ID('tempdb..#K_Out') IS NULL
        BEGIN
            SELECT * INTO #K_Out FROM K_Out 
            WHERE duty_date >= @sDateBegin AND duty_date <= @sDateEnd
        END
        
        IF OBJECT_ID('tempdb..#K_Travel') IS NULL
        BEGIN
            SELECT * INTO #K_Travel FROM K_Travel 
            WHERE duty_date >= @sDateBegin AND duty_date <= @sDateEnd
        END
        
        -- 删除已存在的#GZ_FMU临时表
        IF OBJECT_ID('tempdb..#GZ_FMU') IS NOT NULL
            DROP TABLE #GZ_FMU
            
        -- 创建公式临时表
        SELECT 
            ROW_NUMBER() OVER(ORDER BY A.FMUBM, A.FormulaOrder) AS RowNum,
            CAST(Sql_Final AS NVARCHAR(MAX)) AS Sql_Final,
            Condition,
            B.Table_name,
            A.FMUBM,
            A.FormulaOrder 
        INTO #GZ_FMU 
        FROM GZ_FMU A
        INNER JOIN #FumSet B ON A.FMUBM = B.FmuBm 
        WHERE A.Takeit = 1
        ORDER BY A.FMUBM, A.FormulaOrder
        
        CREATE INDEX IX_GZ_FMU_RowNum ON #GZ_FMU (RowNum)
        
        -- 对公式中的特定字符进行替换（保持原版本逻辑）
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Day.', '#K_Day.')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Day ', '#K_Day ')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Card.', '#K_Card.')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Card ', '#K_Card ')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Leave.', '#K_Leave.')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Leave ', '#K_Leave ')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Over.', '#K_Over.')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Over ', '#K_Over ')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Out.', '#K_Out.')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Out ', '#K_Out ')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Travel.', '#K_Travel.')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, 'K_Travel ', '#K_Travel ')
        
        -- 添加开始结束日期的转换
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, '开始日期', '''' + @sDateBegin + '''')
        UPDATE #GZ_FMU SET Sql_Final = REPLACE(Sql_Final, '结束日期', '''' + @sDateEnd + '''')
        
        -- 声明变量（使用NVARCHAR(MAX)替代TEXT）
        DECLARE @i INT = 1
        DECLARE @ism INT
        DECLARE @Sql_Final NVARCHAR(MAX)
        DECLARE @Sql_Final1 NVARCHAR(MAX) = N''
        DECLARE @MaxSqlLength INT = 1000000  -- 1MB限制
        DECLARE @CurrentLength INT = 0
        
        SELECT @ism = MAX(RowNum) FROM #GZ_FMU
        
        -- 进一步优化：用STRING_AGG一次性拼接（SQL Server 2017+）
        -- 如果是老版本SQL Server，保持原来的WHILE循环
        IF @@VERSION LIKE '%Microsoft SQL Server 201[7-9]%' OR @@VERSION LIKE '%Microsoft SQL Server 202%'
        BEGIN
            -- 新版本：使用STRING_AGG一次性拼接
            SELECT @Sql_Final1 = STRING_AGG(N'UPDATE #K_Day ' + Sql_Final + N';', N'')
            FROM #GZ_FMU
            WHERE Sql_Final IS NOT NULL AND LEN(Sql_Final) > 0
            ORDER BY RowNum
        END
        ELSE
        BEGIN
            -- 老版本：保持你的批量执行逻辑，但增加长度控制
            WHILE @i <= @ism
            BEGIN
                SELECT @Sql_Final = Sql_Final FROM #GZ_FMU WHERE RowNum = @i

                IF @Sql_Final IS NOT NULL AND LEN(@Sql_Final) > 0
                BEGIN
                    DECLARE @NewSql NVARCHAR(MAX) = N' UPDATE #K_Day ' + @Sql_Final + N';'
                    DECLARE @NewLength INT = LEN(@NewSql)

                    -- 检查是否会超过长度限制
                    IF @CurrentLength + @NewLength > @MaxSqlLength
                    BEGIN
                        -- 执行当前批次
                        IF LEN(@Sql_Final1) > 0
                        BEGIN
                            EXEC sp_executesql @Sql_Final1
                            SET @Sql_Final1 = N''
                            SET @CurrentLength = 0
                        END
                    END

                    -- 添加到当前批次
                    SET @Sql_Final1 = @Sql_Final1 + @NewSql
                    SET @CurrentLength = @CurrentLength + @NewLength
                END

                SET @i = @i + 1
            END
        END
        
        -- 执行最后一个批次
        IF LEN(@Sql_Final1) > 0
        BEGIN
            EXEC sp_executesql @Sql_Final1
        END
        
        PRINT '公式计算完成'
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY()
        DECLARE @ErrorState INT = ERROR_STATE()
        
        PRINT '存储过程执行出错: ' + @ErrorMessage
        
        -- 清理临时表
        IF OBJECT_ID('tempdb..#FumSet') IS NOT NULL DROP TABLE #FumSet
        IF OBJECT_ID('tempdb..#GZ_FMU') IS NOT NULL DROP TABLE #GZ_FMU
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState)
    END CATCH
    
    -- 清理临时表
    IF OBJECT_ID('tempdb..#FumSet') IS NOT NULL DROP TABLE #FumSet
    IF OBJECT_ID('tempdb..#GZ_FMU') IS NOT NULL DROP TABLE #GZ_FMU
    
END
